import { MidwayConfig } from '@midwayjs/core';

export default {
  // use for cookie sign key, should change to your own and keep security
  keys: '1748424677125_870',
  koa: {
    port: 7001,
  },
  // JWT配置（必需）
  jwt: {
    secret: 'question-secret',
    expiresIn: '2h', // 访问令牌过期时间
  },
  // 令牌配置（必需）
  token: {
    secret: 'your-token-secret',
    expiresIn: 2 * 3600, // 访问令牌过期时间（秒）
    refreshExpiresIn: 15 * 24 * 3600, // 刷新令牌过期时间（秒）
  },
  // JWT认证组件配置
  jwtAuth: {
    enable: true,
    // 只配置需要覆盖的项
    apiManagerBaseURL: 'http://127.0.0.1:1002', // 根据实际环境修改
  },
} as MidwayConfig;
